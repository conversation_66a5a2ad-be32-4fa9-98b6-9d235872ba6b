import sqlite3

def insert_sample_data():
    conn = sqlite3.connect('forum.db')
    
    # 插入示例用户
    try:
        conn.execute("INSERT INTO users (username, email, password_hash) VALUES ('张三', '<PERSON><PERSON><PERSON>@example.com', 'hash123')")
        conn.execute("INSERT INTO users (username, email, password_hash) VALUES ('李四', '<EMAIL>', 'hash456')")
        conn.execute("INSERT INTO users (username, email, password_hash) VALUES ('王五', '<EMAIL>', 'hash789')")
        print("用户数据插入成功")
    except sqlite3.IntegrityError:
        print("用户数据已存在")
    
    # 插入示例帖子
    try:
        conn.execute("""INSERT INTO posts (title, content, user_id, category_id, views) VALUES 
            ('Python Flask 开发心得分享', '最近在用Flask开发一个论坛项目，想和大家分享一些开发过程中的心得体会。Flask作为一个轻量级的Web框架，具有简洁、灵活的特点，非常适合快速开发小型到中型的Web应用。在这个项目中，我主要使用了Flask的模板系统、路由系统以及SQLite数据库。', 1, 1, 45)""")
        
        conn.execute("""INSERT INTO posts (title, content, user_id, category_id, views) VALUES 
            ('关于简约设计的一些思考', '最近在思考什么是真正的简约设计。简约不是简单，而是在保持功能完整的前提下，去除一切不必要的装饰和复杂性。就像这个论坛一样，我们追求的是内容为王，界面为内容服务，而不是让华丽的界面喧宾夺主。', 2, 2, 32)""")
        
        conn.execute("""INSERT INTO posts (title, content, user_id, category_id, views) VALUES 
            ('学习编程的几个阶段', '回顾自己的编程学习历程，大致可以分为几个阶段：1. 语法学习阶段 - 掌握基本语法和概念；2. 项目实践阶段 - 通过小项目巩固知识；3. 深入理解阶段 - 理解底层原理和设计模式；4. 持续学习阶段 - 跟上技术发展，学习新技术。每个阶段都有其特点和挑战。', 3, 3, 67)""")
        
        conn.execute("""INSERT INTO posts (title, content, user_id, category_id, views) VALUES 
            ('CSS Grid 布局实践总结', '最近在项目中大量使用了CSS Grid布局，相比传统的float和flexbox，Grid在处理二维布局时确实更加强大和直观。特别是在响应式设计方面，Grid的auto-fit和minmax函数组合使用，可以很轻松地实现自适应的网格布局。', 1, 1, 28)""")
        
        conn.execute("""INSERT INTO posts (title, content, user_id, category_id, views) VALUES 
            ('读书笔记：《设计心理学》', '最近读完了唐纳德·诺曼的《设计心理学》，书中提到的"以用户为中心的设计"理念让我印象深刻。好的设计应该是直观的，用户不需要思考就能知道如何使用。这个原则不仅适用于产品设计，也适用于网站和软件界面设计。', 2, 2, 41)""")
        
        conn.execute("""INSERT INTO posts (title, content, user_id, category_id, views) VALUES 
            ('JavaScript 异步编程最佳实践', '异步编程是JavaScript的核心特性之一，从最初的回调函数，到Promise，再到async/await，JavaScript的异步编程模式在不断演进。在实际项目中，我总结了一些异步编程的最佳实践，希望能对大家有所帮助。', 3, 1, 53)""")
        
        print("帖子数据插入成功")
    except Exception as e:
        print(f"帖子数据插入失败: {e}")
    
    conn.commit()
    
    # 检查数据
    categories_count = conn.execute('SELECT COUNT(*) FROM categories').fetchone()[0]
    users_count = conn.execute('SELECT COUNT(*) FROM users').fetchone()[0]
    posts_count = conn.execute('SELECT COUNT(*) FROM posts').fetchone()[0]
    
    print(f"数据库统计:")
    print(f"版块数量: {categories_count}")
    print(f"用户数量: {users_count}")
    print(f"帖子数量: {posts_count}")
    
    conn.close()

if __name__ == "__main__":
    insert_sample_data()
