from flask import Flask, render_template, request, redirect, url_for, flash, session
from datetime import datetime, timedelta
import sqlite3
import os
import hashlib
import string
import random

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 在生产环境中请更改此密钥
app.permanent_session_lifetime = timedelta(days=7)  # 设置session有效期为7天

# 自定义识别码生成算法
def generate_identifier_code(input_string):
    """
    自定义算法：将任意字符串转换为6位字母数字组合
    算法步骤：
    1. 对输入字符串进行多重哈希处理
    2. 使用自定义映射表进行字符转换
    3. 通过位运算和模运算生成最终6位码
    """
    # 字符集：数字+大小写字母（去除容易混淆的字符）
    charset = "0123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz"

    # 第一步：多重哈希
    hash1 = hashlib.md5(input_string.encode()).hexdigest()
    hash2 = hashlib.sha1((input_string + "salt1").encode()).hexdigest()
    hash3 = hashlib.sha256((input_string + "salt2").encode()).hexdigest()

    # 第二步：组合哈希值并进行自定义变换
    combined = hash1 + hash2 + hash3

    # 第三步：自定义映射算法
    result = []
    for i in range(6):
        # 取不同位置的字符进行运算
        pos1 = (i * 13 + 7) % len(combined)
        pos2 = (i * 17 + 11) % len(combined)
        pos3 = (i * 19 + 23) % len(combined)

        # 获取字符的ASCII值
        val1 = ord(combined[pos1])
        val2 = ord(combined[pos2])
        val3 = ord(combined[pos3])

        # 自定义运算：位运算 + 模运算
        final_val = (val1 ^ val2 ^ val3) + (i * 31) + len(input_string)

        # 映射到字符集
        char_index = final_val % len(charset)
        result.append(charset[char_index])

    return ''.join(result)

def create_display_name(nickname, identifier_code):
    """创建显示用的用户名：昵称#识别码"""
    return f"{nickname}#{identifier_code}"

def parse_display_name(display_name):
    """解析显示用户名，返回昵称和识别码"""
    if '#' in display_name:
        parts = display_name.split('#')
        if len(parts) == 2:
            return parts[0], parts[1]
    return display_name, None

# 添加自定义过滤器
@app.template_filter('nl2br')
def nl2br_filter(text):
    """将换行符转换为HTML的<br>标签"""
    if text is None:
        return ''
    return text.replace('\n', '<br>\n')

# 注册过滤器
app.jinja_env.filters['nl2br'] = nl2br_filter

# 数据库配置
DATABASE = 'forum.db'

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    """初始化数据库"""
    conn = get_db_connection()
    
    # 创建版块表
    conn.execute('''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 创建帖子表 - 直接存储用户昵称和识别码
    conn.execute('''
        CREATE TABLE IF NOT EXISTS posts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            nickname TEXT NOT NULL,
            identifier_code TEXT NOT NULL,
            display_name TEXT NOT NULL,
            category_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            views INTEGER DEFAULT 0,
            FOREIGN KEY (category_id) REFERENCES categories (id)
        )
    ''')
    
    # 创建回复表 - 直接存储用户昵称和识别码
    conn.execute('''
        CREATE TABLE IF NOT EXISTS replies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            content TEXT NOT NULL,
            nickname TEXT NOT NULL,
            identifier_code TEXT NOT NULL,
            display_name TEXT NOT NULL,
            post_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (post_id) REFERENCES posts (id)
        )
    ''')
    
    conn.commit()
    conn.close()

@app.route('/')
def index():
    """主页 - 显示所有帖子"""
    conn = get_db_connection()

    # 获取所有帖子
    recent_posts = conn.execute('''
        SELECT p.*, p.display_name as username, c.name as category_name
        FROM posts p
        JOIN categories c ON p.category_id = c.id
        ORDER BY p.created_at DESC
        LIMIT 20
    ''').fetchall()

    conn.close()
    return render_template('index.html', recent_posts=recent_posts)

@app.route('/category/<int:category_id>')
def category(category_id):
    """版块页面 - 显示特定版块的帖子"""
    conn = get_db_connection()
    
    # 获取版块信息
    category = conn.execute('SELECT * FROM categories WHERE id = ?', (category_id,)).fetchone()
    if not category:
        flash('版块不存在')
        return redirect(url_for('index'))
    
    # 获取该版块的帖子
    posts = conn.execute('''
        SELECT p.*, p.display_name as username,
               (SELECT COUNT(*) FROM replies WHERE post_id = p.id) as reply_count
        FROM posts p
        WHERE p.category_id = ?
        ORDER BY p.created_at DESC
    ''', (category_id,)).fetchall()
    
    conn.close()
    return render_template('category.html', category=category, posts=posts)

@app.route('/post/<int:post_id>')
def post_detail(post_id):
    """帖子详情页面"""
    conn = get_db_connection()
    
    # 增加浏览量
    conn.execute('UPDATE posts SET views = views + 1 WHERE id = ?', (post_id,))
    
    # 获取帖子信息
    post = conn.execute('''
        SELECT p.*, p.display_name as username, c.name as category_name
        FROM posts p
        JOIN categories c ON p.category_id = c.id
        WHERE p.id = ?
    ''', (post_id,)).fetchone()

    if not post:
        flash('帖子不存在')
        return redirect(url_for('index'))

    # 获取回复
    replies = conn.execute('''
        SELECT r.*, r.display_name as username
        FROM replies r
        WHERE r.post_id = ?
        ORDER BY r.created_at ASC
    ''', (post_id,)).fetchall()
    
    conn.commit()
    conn.close()
    return render_template('post_detail.html', post=post, replies=replies)

@app.route('/new_post', methods=['GET', 'POST'])
def new_post():
    """发帖页面"""
    conn = get_db_connection()
    categories = conn.execute('SELECT * FROM categories ORDER BY name').fetchall()
    
    if request.method == 'POST':
        # 获取表单数据
        title = request.form.get('title', '').strip()
        content = request.form.get('content', '').strip()
        category_id = request.form.get('category_id', '').strip()
        nickname = request.form.get('nickname', '').strip()
        input_code = request.form.get('input_code', '').strip()
        
        # 验证表单数据
        if not title or not content or not category_id or not nickname or not input_code:
            flash('请填写所有必填字段')
            return render_template('new_post.html', categories=categories)
        
        # 验证昵称长度
        if len(nickname) < 2 or len(nickname) > 20:
            flash('昵称长度应在2-20个字符之间')
            return render_template('new_post.html', categories=categories)
        
        # 验证识别码长度
        if len(input_code) < 6:
            flash('识别码至少需要6个字符')
            return render_template('new_post.html', categories=categories)
        
        # 生成识别码和显示名称
        identifier_code = generate_identifier_code(input_code)
        display_name = create_display_name(nickname, identifier_code)
        
        try:
            # 插入帖子
            conn.execute(
                'INSERT INTO posts (title, content, nickname, identifier_code, display_name, category_id) VALUES (?, ?, ?, ?, ?, ?)',
                (title, content, nickname, identifier_code, display_name, category_id)
            )
            conn.commit()
            
            # 获取新帖子ID
            post_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
            
            flash('发帖成功！')
            conn.close()
            return redirect(url_for('post_detail', post_id=post_id))
        except Exception as e:
            flash('发帖失败，请重试')
            conn.close()
            return render_template('new_post.html', categories=categories)
    
    conn.close()
    return render_template('new_post.html', categories=categories)

@app.route('/post/<int:post_id>/reply', methods=['POST'])
def post_reply(post_id):
    """发表回复"""
    conn = get_db_connection()
    
    # 检查帖子是否存在
    post = conn.execute('SELECT * FROM posts WHERE id = ?', (post_id,)).fetchone()
    if not post:
        flash('帖子不存在')
        return redirect(url_for('index'))
    
    # 获取表单数据
    content = request.form.get('content', '').strip()
    nickname = request.form.get('nickname', '').strip()
    input_code = request.form.get('input_code', '').strip()
    
    # 验证表单数据
    if not content or not nickname or not input_code:
        flash('请填写所有必填字段')
        return redirect(url_for('post_detail', post_id=post_id))
    
    # 验证昵称长度
    if len(nickname) < 2 or len(nickname) > 20:
        flash('昵称长度应在2-20个字符之间')
        return redirect(url_for('post_detail', post_id=post_id))
    
    # 验证识别码长度
    if len(input_code) < 6:
        flash('识别码至少需要6个字符')
        return redirect(url_for('post_detail', post_id=post_id))
    
    # 生成识别码和显示名称
    identifier_code = generate_identifier_code(input_code)
    display_name = create_display_name(nickname, identifier_code)
    
    try:
        # 插入回复
        conn.execute(
            'INSERT INTO replies (content, nickname, identifier_code, display_name, post_id) VALUES (?, ?, ?, ?, ?)',
            (content, nickname, identifier_code, display_name, post_id)
        )
        conn.commit()
        flash('回复成功！')
    except Exception as e:
        flash('回复失败，请重试')
    
    conn.close()
    return redirect(url_for('post_detail', post_id=post_id))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面 - 使用昵称和识别码登录"""
    # 如果用户已登录，直接跳转到首页
    if 'nickname' in session and 'input_code' in session:
        flash('您已经登录')
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        nickname = request.form.get('nickname', '').strip()
        input_code = request.form.get('input_code', '').strip()
        
        # 验证表单数据
        if not nickname or not input_code:
            flash('请填写所有必填字段')
            return render_template('login.html')
        
        # 验证昵称长度
        if len(nickname) < 2 or len(nickname) > 20:
            flash('昵称长度应在2-20个字符之间')
            return render_template('login.html')
        
        # 验证识别码不为空即可
        if not input_code:
            flash('请输入识别码')
            return render_template('login.html')
        
        # 生成识别码和显示名称
        identifier_code = generate_identifier_code(input_code)
        display_name = create_display_name(nickname, identifier_code)
        
        # 设置session
        session.permanent = True  # 使用永久session
        session['nickname'] = nickname
        session['input_code'] = input_code
        session['identifier_code'] = identifier_code
        session['display_name'] = display_name
        
        flash('登录成功！')
        return redirect(url_for('index'))
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """退出登录"""
    # 清除session
    session.pop('nickname', None)
    session.pop('input_code', None)
    session.pop('identifier_code', None)
    session.pop('display_name', None)
    
    flash('您已退出登录')
    return redirect(url_for('index'))

if __name__ == '__main__':
    # 检查数据库是否存在，如果不存在则提示用户运行初始化脚本
    if not os.path.exists('forum.db'):
        print("数据库文件不存在！")
        print("请先运行以下命令初始化数据库：")
        print("python init_db.py")
        exit(1)

    app.run(debug=True, host='0.0.0.0', port=5000)
