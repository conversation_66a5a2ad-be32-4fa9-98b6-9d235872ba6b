<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}简约论坛{% endblock %}</title>
    
    <!-- Google Fonts - Noto Serif Simplified Chinese -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="{{ url_for('index') }}">简约论坛</a>
            </div>
            
            <div class="nav-menu">
                <a href="{{ url_for('index') }}" class="nav-link">主页</a>
                <a href="{{ url_for('category', category_id=1) }}" class="nav-link">技术讨论</a>
                <a href="{{ url_for('category', category_id=2) }}" class="nav-link">生活随笔</a>
                <a href="{{ url_for('category', category_id=3) }}" class="nav-link">学习交流</a>
                {% if session.nickname %}
                    <span class="nav-link user-info">{% set parts = session.display_name.split('#') %}{% if parts|length > 1 %}<span class="display-name-part">{{ parts[0] }}</span><span class="identifier-code-part">#{{ parts[1] }}</span>{% else %}{{ session.display_name }}{% endif %}</span>
                    <a href="{{ url_for('logout') }}" class="nav-link">退出</a>
                    <a href="{{ url_for('new_post') }}" class="nav-link nav-register">发布新帖</a>
                {% else %}
                    <a href="{{ url_for('login') }}" class="nav-link">登录</a>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <div class="container">
            <!-- Flash 消息 -->
            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    <div class="flash-messages">
                        {% for message in messages %}
                            <div class="flash-message">{{ message }}</div>
                        {% endfor %}
                    </div>
                    <script>
                        // 自动移除flash消息
                        document.addEventListener('DOMContentLoaded', function() {
                            const flashMessages = document.querySelectorAll('.flash-message');
                            flashMessages.forEach(function(message) {
                                // 4秒后移除消息
                                setTimeout(function() {
                                    message.parentNode.removeChild(message);
                                }, 4000);
                            });
                        });
                    </script>
                {% endif %}
            {% endwith %}

            <!-- 页面内容 -->
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2024 简约论坛. 优雅简洁的交流空间.</p>
                <div class="footer-links">
                    <a href="#" onclick="window.scrollTo({top: 0, behavior: 'smooth'}); return false;">回到顶部</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
