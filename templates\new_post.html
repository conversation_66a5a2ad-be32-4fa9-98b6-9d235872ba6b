{% extends "base.html" %}

{% block title %}发布新帖 - 简约论坛{% endblock %}

{% block content %}
<div class="new-post-page">
    <div class="page-header">
        <h1 class="page-title">发布新帖</h1>
        <p class="page-subtitle">分享你的想法，与大家交流讨论</p>
    </div>

    <div class="post-form-container">
        <form class="post-form" method="POST" action="{{ url_for('new_post') }}">
            <div class="form-row">
                <div class="form-group">
                    <label for="category" class="form-label">选择版块</label>
                    <select id="category" name="category_id" class="form-select" required>
                        <option value="">请选择版块</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" 
                                {% if request.args.get('category') == category.id|string %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label for="title" class="form-label">帖子标题</label>
                <input 
                    type="text" 
                    id="title" 
                    name="title" 
                    class="form-input" 
                    placeholder="请输入帖子标题（5-100个字符）"
                    required
                    minlength="5"
                    maxlength="100"
                >
                <small class="form-help">一个好的标题能吸引更多人参与讨论</small>
            </div>

            <div class="form-group">
                <label for="content" class="form-label">帖子内容</label>
                <div class="editor-toolbar">
                    <button type="button" class="toolbar-btn" data-action="bold" title="粗体">
                        <strong>B</strong>
                    </button>
                    <button type="button" class="toolbar-btn" data-action="italic" title="斜体">
                        <em>I</em>
                    </button>
                    <button type="button" class="toolbar-btn" data-action="link" title="链接">
                        🔗
                    </button>
                    <button type="button" class="toolbar-btn" data-action="image" title="图片">
                        🖼️
                    </button>
                    <button type="button" class="toolbar-btn" data-action="code" title="代码">
                        &lt;/&gt;
                    </button>
                </div>
                <textarea 
                    id="content" 
                    name="content" 
                    class="form-textarea" 
                    placeholder="请输入帖子内容，支持Markdown格式..."
                    rows="15"
                    required
                    minlength="10"
                ></textarea>
                <small class="form-help">支持Markdown语法，最少10个字符</small>
            </div>

            <div class="form-group">
                <label class="form-label">发布选项</label>
                <div class="form-options">
                    <label class="checkbox-label">
                        <input type="checkbox" name="allow_reply" class="form-checkbox" checked>
                        <span class="checkbox-text">允许回复</span>
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" name="notify_reply" class="form-checkbox" checked>
                        <span class="checkbox-text">有新回复时通知我</span>
                    </label>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">发帖身份</label>
                {% if session.nickname %}
                    <!-- 已登录用户，显示当前身份并使用隐藏字段传递值 -->
                    <div class="user-identity-info">
                        <p>您当前的身份: <strong>{{ session.display_name }}</strong></p>
                        <input type="hidden" name="nickname" value="{{ session.nickname }}">
                        <input type="hidden" name="input_code" value="{{ session.input_code }}">
                    </div>
                {% else %}
                    <!-- 未登录用户，显示输入字段 -->
                    <div class="form-row">
                        <div class="form-group" style="flex: 1;">
                            <label for="nickname" class="form-label">昵称</label>
                            <input 
                                type="text" 
                                id="nickname" 
                                name="nickname" 
                                class="form-input" 
                                placeholder="请输入您的昵称（2-20个字符）"
                                required
                                minlength="2"
                                maxlength="20"
                            >
                        </div>
                        <div class="form-group" style="flex: 1;">
                            <label for="input_code" class="form-label">识别码</label>
                            <input 
                                type="password" 
                                id="input_code" 
                                name="input_code" 
                                class="form-input" 
                                placeholder="请输入您的识别码（至少6个字符）"
                                required
                                minlength="6"
                            >
                            <small class="form-help">识别码用于生成您的唯一标识，请牢记您的昵称和识别码</small>
                        </div>
                    </div>
                    <div id="display-name-preview" class="display-name-preview" style="margin-top: 10px; font-weight: bold;"></div>
                    <div class="login-suggestion" style="margin-top: 10px;">
                        <p>提示: <a href="{{ url_for('login') }}">登录</a> 后可以自动使用您的身份信息</p>
                    </div>
                {% endif %}
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">发布帖子</button>
                <button type="button" class="btn btn-outline" id="preview-btn">预览</button>
                <button type="button" class="btn btn-outline" id="save-draft-btn">保存草稿</button>
                <a href="{{ url_for('index') }}" class="btn btn-text">取消</a>
            </div>
        </form>
    </div>

    <!-- 预览区域 -->
    <div class="preview-container" id="preview-container" style="display: none;">
        <div class="preview-header">
            <h3>预览效果</h3>
            <button type="button" class="btn btn-text" id="close-preview">关闭预览</button>
        </div>
        <div class="preview-content" id="preview-content">
            <!-- 预览内容将在这里显示 -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 简单的编辑器功能
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.getElementById('content');
    const previewBtn = document.getElementById('preview-btn');
    const previewContainer = document.getElementById('preview-container');
    const previewContent = document.getElementById('preview-content');
    const closePreviewBtn = document.getElementById('close-preview');
    const postTitle = document.getElementById('title');
    const nicknameInput = document.getElementById('nickname');
    const inputCodeInput = document.getElementById('input_code');
    const displayNamePreview = document.getElementById('display-name-preview');

    // 生成6位识别码的函数
    function generateIdentifierCode(input) {
        // 使用简单的哈希算法
        let hash = 0;
        for (let i = 0; i < input.length; i++) {
            const char = input.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        // 确保是正数
        hash = Math.abs(hash);
        
        // 转换为6位字母数字组合
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let code = '';
        for (let i = 0; i < 6; i++) {
            code += characters.charAt(hash % characters.length);
            hash = Math.floor(hash / characters.length);
        }
        
        return code;
    }

    // 更新显示名称预览
    function updateDisplayNamePreview() {
        const nickname = nicknameInput.value.trim();
        const inputCode = inputCodeInput.value.trim();
        
        if (nickname && inputCode) {
            const identifierCode = generateIdentifierCode(inputCode);
            displayNamePreview.textContent = `您的显示名称: ${nickname}#${identifierCode}`;
        } else {
            displayNamePreview.textContent = '';
        }
    }

    // 监听输入变化
    nicknameInput.addEventListener('input', updateDisplayNamePreview);
    inputCodeInput.addEventListener('input', updateDisplayNamePreview);

    // 预览功能
    if (previewBtn) {
        previewBtn.addEventListener('click', function() {
            const title = postTitle.value || '无标题';
            const content = textarea.value || '无内容';
            const nickname = nicknameInput.value.trim() || '匿名用户';
            const inputCode = inputCodeInput.value.trim();
            let displayName = nickname;
            
            if (inputCode) {
                const identifierCode = generateIdentifierCode(inputCode);
                displayName = `${nickname}#${identifierCode}`;
            }
            
            // 设置预览内容
            previewContent.innerHTML = `
                <h2 class="preview-title">${title}</h2>
                <div class="preview-author">作者: ${displayName}</div>
                <div class="preview-body">${content.replace(/\n/g, '<br>')}</div>
            `;
            previewContainer.style.display = 'block';
        });
    }

    if (closePreviewBtn) {
        closePreviewBtn.addEventListener('click', function() {
            previewContainer.style.display = 'none';
        });
    }
});
</script>
{% endblock %}
