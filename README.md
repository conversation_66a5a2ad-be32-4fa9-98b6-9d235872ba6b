# 简约论坛

一个基于Python Flask的简洁优雅论坛系统，使用SQLite数据库存储数据，全局采用Noto Serif Simplified Chinese字体。

## 特性

- 🎨 简洁优雅的UI设计
- 📱 响应式布局，支持移动端
- 🔐 用户注册和登录系统
- 📝 发帖和回复功能
- 🏷️ 版块分类管理
- 💾 SQLite数据库存储
- 🌏 全局中文字体支持

## 技术栈

- **后端**: Python Flask
- **数据库**: SQLite
- **前端**: HTML5, CSS3, JavaScript
- **字体**: Noto Serif Simplified Chinese

## 项目结构

```
HcbbsNew/
├── app.py                 # Flask应用主文件
├── requirements.txt       # Python依赖包
├── forum.db              # SQLite数据库文件（运行后自动生成）
├── templates/            # HTML模板文件
│   ├── base.html         # 基础模板
│   ├── index.html        # 首页
│   ├── category.html     # 版块页面
│   ├── post_detail.html  # 帖子详情页
│   ├── login.html        # 登录页面
│   ├── register.html     # 注册页面
│   └── new_post.html     # 发帖页面
└── static/               # 静态资源
    ├── css/
    │   └── style.css     # 主样式文件
    ├── js/
    │   └── main.js       # JavaScript功能
    └── images/           # 图片资源
        └── .gitkeep
```

## 安装和运行

### 1. 克隆项目

```bash
git clone <repository-url>
cd HcbbsNew
```

### 2. 创建虚拟环境（推荐）

```bash
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 运行应用

```bash
python app.py
```

应用将在 `http://localhost:5000` 启动。

## 默认数据

首次运行时，系统会自动创建以下默认版块：

- 技术讨论 - 分享技术心得，讨论编程问题
- 生活随笔 - 记录生活点滴，分享日常感悟  
- 学习交流 - 学习资源分享，经验交流

## 功能说明

### 当前已实现的UI界面

- ✅ 首页 - 显示版块列表和最新帖子
- ✅ 版块页面 - 显示特定版块的帖子列表
- ✅ 帖子详情页 - 显示帖子内容和回复
- ✅ 登录页面 - 用户登录界面
- ✅ 注册页面 - 用户注册界面
- ✅ 发帖页面 - 发布新帖子界面

### 待实现的后端功能

- ⏳ 用户注册和登录逻辑
- ⏳ 发帖功能实现
- ⏳ 回复功能实现
- ⏳ 用户权限管理
- ⏳ 帖子编辑和删除
- ⏳ 搜索功能
- ⏳ 分页功能

## 设计特色

- **简约美观**: 采用现代简约设计风格，界面清爽
- **中文优化**: 全局使用Noto Serif Simplified Chinese字体，中文显示效果佳
- **响应式**: 支持桌面端和移动端访问
- **用户体验**: 注重交互细节，提供良好的用户体验

## 开发说明

这是一个基础UI框架，主要展示了论坛的界面设计和布局。后端功能（如用户认证、数据处理等）需要进一步开发实现。

## 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
