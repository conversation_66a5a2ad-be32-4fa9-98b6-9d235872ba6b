#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
用于创建无需注册的匿名论坛数据库结构
"""

import sqlite3
import os
import hashlib

def generate_identifier_code(input_string):
    """
    自定义算法：将任意字符串转换为6位字母数字组合
    """
    # 字符集：数字+大小写字母（去除容易混淆的字符）
    charset = "0123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz"
    
    # 第一步：多重哈希
    hash1 = hashlib.md5(input_string.encode()).hexdigest()
    hash2 = hashlib.sha1((input_string + "salt1").encode()).hexdigest()
    hash3 = hashlib.sha256((input_string + "salt2").encode()).hexdigest()
    
    # 第二步：组合哈希值并进行自定义变换
    combined = hash1 + hash2 + hash3
    
    # 第三步：自定义映射算法
    result = []
    for i in range(6):
        # 取不同位置的字符进行运算
        pos1 = (i * 13 + 7) % len(combined)
        pos2 = (i * 17 + 11) % len(combined)
        pos3 = (i * 19 + 23) % len(combined)
        
        # 获取字符的ASCII值
        val1 = ord(combined[pos1])
        val2 = ord(combined[pos2]) 
        val3 = ord(combined[pos3])
        
        # 自定义运算：位运算 + 模运算
        final_val = (val1 ^ val2 ^ val3) + (i * 31) + len(input_string)
        
        # 映射到字符集
        char_index = final_val % len(charset)
        result.append(charset[char_index])
    
    return ''.join(result)

def create_display_name(nickname, identifier_code):
    """创建显示用的用户名：昵称#识别码"""
    return f"{nickname}#{identifier_code}"

def init_database():
    """初始化数据库"""
    # 删除旧数据库文件（如果存在）
    if os.path.exists('forum.db'):
        print("删除旧数据库文件...")
        os.remove('forum.db')
    
    print("创建新数据库...")
    conn = sqlite3.connect('forum.db')
    conn.row_factory = sqlite3.Row
    
    print("创建版块表...")
    # 创建版块表
    conn.execute('''
        CREATE TABLE categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    print("创建帖子表...")
    # 创建帖子表 - 直接存储用户昵称和识别码
    conn.execute('''
        CREATE TABLE posts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            nickname TEXT NOT NULL,
            identifier_code TEXT NOT NULL,
            display_name TEXT NOT NULL,
            category_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            views INTEGER DEFAULT 0,
            FOREIGN KEY (category_id) REFERENCES categories (id)
        )
    ''')
    
    print("创建回复表...")
    # 创建回复表 - 直接存储用户昵称和识别码
    conn.execute('''
        CREATE TABLE replies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            content TEXT NOT NULL,
            nickname TEXT NOT NULL,
            identifier_code TEXT NOT NULL,
            display_name TEXT NOT NULL,
            post_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (post_id) REFERENCES posts (id)
        )
    ''')
    
    print("插入示例版块...")
    # 插入示例版块
    categories_data = [
        ('技术讨论', '分享技术心得，讨论编程问题'),
        ('生活随笔', '记录生活点滴，分享日常感悟'),
        ('学习交流', '学习资源分享，经验交流')
    ]
    
    for name, description in categories_data:
        conn.execute(
            "INSERT INTO categories (name, description) VALUES (?, ?)",
            (name, description)
        )
    
    print("插入示例帖子...")
    # 插入示例帖子
    posts_data = [
        ('Python Flask 开发心得分享', 
         '最近在用Flask开发一个论坛项目，想和大家分享一些开发过程中的心得体会。Flask作为一个轻量级的Web框架，具有简洁、灵活的特点，非常适合快速开发小型到中型的Web应用。在这个项目中，我主要使用了Flask的模板系统、路由系统以及SQLite数据库。\n\n特别是这次我们采用了创新的用户认证系统，使用昵称+识别码的方式，让用户管理更加简单直观。', 
         '张三', 'demo123456', 1, 45),
        
        ('关于简约设计的一些思考', 
         '最近在思考什么是真正的简约设计。简约不是简单，而是在保持功能完整的前提下，去除一切不必要的装饰和复杂性。就像这个论坛一样，我们追求的是内容为王，界面为内容服务，而不是让华丽的界面喧宾夺主。\n\n新的用户系统也体现了这一理念，不需要复杂的邮箱验证，只需要昵称和识别码就能创建独特的身份标识。', 
         '李四', 'test789012', 2, 32),
        
        ('学习编程的几个阶段', 
         '回顾自己的编程学习历程，大致可以分为几个阶段：\n\n1. 语法学习阶段 - 掌握基本语法和概念\n2. 项目实践阶段 - 通过小项目巩固知识\n3. 深入理解阶段 - 理解底层原理和设计模式\n4. 持续学习阶段 - 跟上技术发展，学习新技术\n\n每个阶段都有其特点和挑战。现在做这个论坛项目，就是很好的实践机会。', 
         '王五', 'sample345678', 3, 67),
        
        ('CSS Grid 布局实践总结', 
         '最近在项目中大量使用了CSS Grid布局，相比传统的float和flexbox，Grid在处理二维布局时确实更加强大和直观。特别是在响应式设计方面，Grid的auto-fit和minmax函数组合使用，可以很轻松地实现自适应的网格布局。\n\n在这个论坛项目中，我们也用到了Grid来实现页面布局，效果很不错。', 
         '张三', 'demo123456', 1, 28),
        
        ('读书笔记：《设计心理学》', 
         '最近读完了唐纳德·诺曼的《设计心理学》，书中提到的"以用户为中心的设计"理念让我印象深刻。好的设计应该是直观的，用户不需要思考就能知道如何使用。\n\n这个原则不仅适用于产品设计，也适用于网站和软件界面设计。我们的论坛就尝试体现这一点，让用户操作尽可能简单直观。', 
         '李四', 'test789012', 2, 41),
        
        ('JavaScript 异步编程最佳实践', 
         '异步编程是JavaScript的核心特性之一，从最初的回调函数，到Promise，再到async/await，JavaScript的异步编程模式在不断演进。\n\n在实际项目中，我总结了一些异步编程的最佳实践：\n\n1. 优先使用async/await而不是Promise.then()\n2. 合理处理错误，使用try-catch\n3. 避免回调地狱\n4. 理解事件循环机制\n\n希望能对大家有所帮助。', 
         '王五', 'sample345678', 1, 53)
    ]
    
    for title, content, nickname, input_code, category_id, views in posts_data:
        # 生成识别码和显示名称
        identifier_code = generate_identifier_code(input_code)
        display_name = create_display_name(nickname, identifier_code)
        
        conn.execute(
            "INSERT INTO posts (title, content, nickname, identifier_code, display_name, category_id, views) VALUES (?, ?, ?, ?, ?, ?, ?)",
            (title, content, nickname, identifier_code, display_name, category_id, views)
        )
    
    conn.commit()
    
    # 统计信息
    categories_count = conn.execute('SELECT COUNT(*) FROM categories').fetchone()[0]
    posts_count = conn.execute('SELECT COUNT(*) FROM posts').fetchone()[0]
    
    print(f"\n数据库初始化完成！")
    print(f"版块数量: {categories_count}")
    print(f"帖子数量: {posts_count}")
    
    print(f"\n示例发帖信息：")
    print("  在发帖或回复时，使用以下昵称和识别码组合：")
    print("  昵称: 张三, 识别码: demo123456")
    print("  昵称: 李四, 识别码: test789012")
    print("  昵称: 王五, 识别码: sample345678")
    print()
    
    conn.close()

if __name__ == "__main__":
    init_database()
