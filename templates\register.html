{% extends "base.html" %}

{% block title %}注册 - 简约论坛{% endblock %}

{% block content %}
<div class="auth-page">
    <div class="auth-container">
        <div class="auth-header">
            <h1 class="auth-title">注册</h1>
            <p class="auth-subtitle">创建您的专属论坛身份</p>
        </div>

        <form class="auth-form" method="POST">
            <div class="form-group">
                <label for="nickname" class="form-label">昵称</label>
                <input
                    type="text"
                    id="nickname"
                    name="nickname"
                    class="form-input"
                    placeholder="请输入昵称（2-20个字符）"
                    required
                    minlength="2"
                    maxlength="20"
                >
                <small class="form-help">昵称是您在论坛的基础名称</small>
            </div>

            <div class="form-group">
                <label for="input_code" class="form-label">识别码</label>
                <input
                    type="password"
                    id="input_code"
                    name="input_code"
                    class="form-input"
                    placeholder="请输入识别码（至少6个字符）"
                    required
                    minlength="6"
                >
                <small class="form-help">识别码可以是任意字符串，用于生成您的唯一标识</small>
            </div>

            <div class="form-group">
                <div class="identifier-preview">
                    <h4>您的论坛显示名称预览：</h4>
                    <div class="preview-box">
                        <span id="preview-name">请输入昵称和识别码</span>
                    </div>
                    <small class="form-help">最终显示格式：昵称#加密识别码</small>
                </div>
            </div>

            <button type="submit" class="btn btn-primary btn-full">注册</button>
        </form>

        <div class="auth-footer">
            <p>已有账号？ <a href="{{ url_for('login') }}" class="auth-link">立即登录</a></p>
            <div class="auth-info">
                <h4>注册说明：</h4>
                <p>• 昵称：您在论坛的基础名称，2-20个字符</p>
                <p>• 识别码：任意字符串，至少6个字符，用于生成唯一标识</p>
                <p>• 系统会将识别码加密为6位字母数字组合</p>
                <p>• 最终显示名称格式：昵称#加密码（如：小明#1Fv5gh）</p>
                <p>• 请记住您的昵称和识别码，登录时需要使用</p>
            </div>
        </div>
    </div>
</div>

<script>
// 简化版的识别码生成算法（仅用于预览）
function generatePreviewCode(inputString) {
    if (!inputString) return '';

    const charset = "0123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz";
    let hash = 0;

    // 简单哈希算法
    for (let i = 0; i < inputString.length; i++) {
        hash = ((hash << 5) - hash + inputString.charCodeAt(i)) & 0xffffffff;
    }

    let result = '';
    for (let i = 0; i < 6; i++) {
        hash = ((hash * 1103515245 + 12345) & 0xffffffff);
        result += charset[Math.abs(hash) % charset.length];
    }

    return result;
}

// 实时更新预览
function updatePreview() {
    const nickname = document.getElementById('nickname').value.trim();
    const inputCode = document.getElementById('input_code').value.trim();
    const previewElement = document.getElementById('preview-name');

    if (nickname && inputCode) {
        const previewCode = generatePreviewCode(inputCode);
        previewElement.textContent = `${nickname}#${previewCode}`;
        previewElement.style.color = '#2c3e50';
    } else {
        previewElement.textContent = '请输入昵称和识别码';
        previewElement.style.color = '#999';
    }
}

// 绑定事件
document.getElementById('nickname').addEventListener('input', updatePreview);
document.getElementById('input_code').addEventListener('input', updatePreview);
</script>

{% endblock %}
