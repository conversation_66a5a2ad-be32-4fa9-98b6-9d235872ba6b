{% extends "base.html" %}

{% block title %}{{ post.title }} - 简约论坛{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 回复排序切换功能
        const sortToggle = document.getElementById('sort-toggle');
        const repliesList = document.querySelector('.replies-list');
        let isAscending = true; // 默认正序

        if (sortToggle && repliesList) {
            sortToggle.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 切换排序方式
                isAscending = !isAscending;
                
                // 更新排序按钮文本
                sortToggle.textContent = isAscending ? '按时间正序' : '按时间倒序';
                
                // 获取所有回复项
                const replyItems = Array.from(repliesList.querySelectorAll('.reply-item'));
                
                // 反转回复顺序
                replyItems.reverse().forEach(item => {
                    repliesList.appendChild(item);
                });
                
                // 更新回复编号
                replyItems.forEach((item, index) => {
                    const replyNumber = item.querySelector('.reply-number');
                    if (replyNumber) {
                        replyNumber.textContent = `#${index + 1}`;
                    }
                });
            });
        }
        
        // 只有在未登录状态下才需要显示名称预览
        {% if not session.nickname %}
            const nicknameInput = document.getElementById('nickname');
            const inputCodeInput = document.getElementById('input_code');
            const displayNamePreview = document.getElementById('display-name-preview');

            // 如果页面上有这些元素
            if (nicknameInput && inputCodeInput && displayNamePreview) {
                // 生成6位识别码的函数
                function generateIdentifierCode(input) {
                    // 使用简单的哈希算法
                    let hash = 0;
                    for (let i = 0; i < input.length; i++) {
                        const char = input.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash; // 转换为32位整数
                    }
                    
                    // 确保是正数
                    hash = Math.abs(hash);
                    
                    // 转换为6位字母数字组合
                    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                    let code = '';
                    for (let i = 0; i < 6; i++) {
                        code += characters.charAt(hash % characters.length);
                        hash = Math.floor(hash / characters.length);
                    }
                    
                    return code;
                }

                // 更新显示名称预览
                function updateDisplayNamePreview() {
                    const nickname = nicknameInput.value.trim();
                    const inputCode = inputCodeInput.value.trim();
                    
                    if (nickname && inputCode) {
                        const identifierCode = generateIdentifierCode(inputCode);
                        displayNamePreview.textContent = `您的显示名称: ${nickname}#${identifierCode}`;
                    } else {
                        displayNamePreview.textContent = '';
                    }
                }

                // 监听输入变化
                nicknameInput.addEventListener('input', updateDisplayNamePreview);
                inputCodeInput.addEventListener('input', updateDisplayNamePreview);
            }
        {% endif %}
    });
</script>
{% endblock %}

{% block content %}
<div class="post-detail-page">

    <!-- 主帖内容 -->
    <article class="main-post">
        <header class="post-header">
            <h1 class="post-title">{{ post.title }}</h1>
            <div class="post-meta">
                <div class="author-info">
                    <div class="author-details">
                        <span class="author-name">{% set parts = post.username.split('#') %}{% if parts|length > 1 %}<span class="display-name-part">{{ parts[0] }}</span><span class="identifier-code-part">#{{ parts[1] }}</span>{% else %}{{ post.username }}{% endif %}</span>
                        <span class="post-time">发布于 {{ post.created_at }}</span>
                    </div>
                </div>
                <div class="post-stats">
                    <span class="stat-item">{{ post.views }} 浏览</span>
                    <span class="stat-item">{{ replies|length }} 回复</span>
                </div>
            </div>
        </header>

        <div class="post-content">
            {{ post.content|nl2br|safe }}
        </div>

        <footer class="post-footer">
            {% if post.updated_at != post.created_at %}
            <div class="post-updated">
                最后编辑于 {{ post.updated_at }}
            </div>
            {% endif %}
        </footer>
    </article>

    <!-- 回复列表 -->
    <section class="replies-section">
        <div class="replies-header">
            <h2 class="replies-title">回复 ({{ replies|length }})</h2>
            <div class="replies-sort">
                <a href="#" id="sort-toggle" class="sort-toggle">按时间正序</a>
            </div>
        </div>

        <div class="replies-list">
            {% for reply in replies %}
            <article class="reply-item" id="reply-{{ reply.id }}">
                <div class="reply-header">
                    <div class="reply-author-info">
                        <span class="author-name">{% set parts = reply.username.split('#') %}{% if parts|length > 1 %}<span class="display-name-part">{{ parts[0] }}</span><span class="identifier-code-part">#{{ parts[1] }}</span>{% else %}{{ reply.username }}{% endif %}</span>
                        <span class="reply-time">{{ reply.created_at }}</span>
                    </div>
                    <span class="reply-number">#{{ loop.index }}</span>
                </div>
                <div class="reply-content">
                    {{ reply.content|nl2br|safe }}
                </div>
            </article>
            {% endfor %}
        </div>
    </section>

    <!-- 回复表单 -->
    <section class="reply-form-section">
        <div class="reply-form-container">
            <h3 class="form-title">发表回复</h3>
            <form class="reply-form" method="POST" action="{{ url_for('post_reply', post_id=post.id) }}">
                <div class="form-group">
                    <textarea 
                        name="content" 
                        class="form-textarea" 
                        placeholder="写下你的回复..." 
                        rows="6" 
                        required
                    ></textarea>
                </div>
                <div class="form-group">
                    {% if session.nickname %}
                        <!-- 已登录用户，使用隐藏字段传递值 -->
                        <input type="hidden" name="nickname" value="{{ session.nickname }}">
                        <input type="hidden" name="input_code" value="{{ session.input_code }}">
                    {% else %}
                        <label class="form-label">回复身份</label>
                        <!-- 未登录用户，显示输入字段 -->
                        <div class="form-row">
                            <div class="form-group" style="flex: 1;">
                                <label for="nickname" class="form-label">昵称</label>
                                <input 
                                    type="text" 
                                    id="nickname" 
                                    name="nickname" 
                                    class="form-input" 
                                    placeholder="请输入您的昵称（2-20个字符）"
                                    required
                                    minlength="2"
                                    maxlength="20"
                                >
                            </div>
                            <div class="form-group" style="flex: 1;">
                                <label for="input_code" class="form-label">识别码</label>
                                <input 
                                    type="password" 
                                    id="input_code" 
                                    name="input_code" 
                                    class="form-input" 
                                    placeholder="请输入您的识别码（至少6个字符）"
                                    required
                                    minlength="6"
                                >
                                <small class="form-help">识别码用于生成您的唯一标识，请牢记您的昵称和识别码</small>
                            </div>
                        </div>
                        <div id="display-name-preview" class="display-name-preview" style="margin-top: 10px; font-weight: bold;"></div>
                        <div class="login-suggestion" style="margin-top: 10px;">
                            <p>提示: <a href="{{ url_for('login') }}">登录</a> 后可以自动使用您的身份信息</p>
                        </div>
                    {% endif %}
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">发布回复</button>
                </div>
            </form>
        </div>
    </section>
</div>

{% endblock %}
