/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Serif SC', serif;
    font-weight: 400;
    line-height: 1.6;
    color: #2c3e50;
    background-color: #fafbfc;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏 */
.navbar {
    background: #fff;
    border-top: 1px solid #000;
    border-bottom: 1px solid #000;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
}

.nav-brand a {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    text-decoration: none;
    letter-spacing: 0.5px;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    color: #5a6c7d;
    text-decoration: none;
    font-weight: 400;
    transition: color 0.3s ease;
    padding: 0.5rem 0;
}

.nav-link:hover {
    color: #000;
}

.nav-register {
    background: transparent;
    color: #000 !important;
    padding: 0.5rem 1rem;
    border: 1px solid #000;
    transition: all 0.3s ease;
}

.nav-register:hover {
    background: #000;
    color: white !important;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.username {
    font-weight: 500;
    color: #2c3e50;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    padding: 2rem 0;
}

/* Flash 消息 - 美观的右上角弹窗 */
.flash-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 300px;
    width: auto;
}

.flash-message {
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    padding: 12px 20px;
    margin-bottom: 10px;
    border-radius: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    animation: slideIn 0.3s ease-out forwards, fadeOut 0.5s ease-out 3s forwards;
    border-left: 4px solid #000;
    backdrop-filter: blur(5px);
    border: 1px solid #000;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
}

.btn-primary {
    background: transparent;
    color: #000;
    border: 1px solid #000;
}

.btn-primary:hover {
    background: #000;
    color: white;
}

.btn-outline {
    background: transparent;
    color: #000;
    border: 1px solid #000;
}

.btn-outline:hover {
    background: #000;
    color: white;
}

.btn-text {
    background: transparent;
    color: #5a6c7d;
    padding: 0.5rem 1rem;
}

.btn-text:hover {
    color: #3498db;
}

.btn-full {
    width: 100%;
}

/* 首页样式 */
.homepage {
    max-width: 1200px;
    margin: 0 auto;
}

.homepage-layout {
    display: flex;
    gap: 3rem;
    align-items: flex-start;
}

.posts-main {
    flex: 2;
}

.sidebar {
    flex: 1;
    position: sticky;
    top: 100px;
}

/* 帖子列表 */
.post-item {
    padding: 1.2rem 0;
    border-bottom: 1px solid #000;
    transition: background-color 0.3s ease;
    background: #fafbfc;
}

.post-item:hover {
    background-color: #f1f3f5;
}

.post-item:last-child {
    border-bottom: 1px solid #000;
}

.post-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.3rem;
    font-size: 0.85rem;
    color: #5a6c7d;
}

.post-category {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.2rem 0.5rem;
    font-size: 0.8rem;
    font-weight: 500;
}

.post-title a {
    color: #2c3e50;
    text-decoration: none;
    font-size: 1.05rem;
    font-weight: 500;
    line-height: 1.3;
}

.post-title a:hover {
    color: #666;
}

.post-excerpt {
    color: #5a6c7d;
    margin: 0.3rem 0 0.5rem 0;
    line-height: 1.4;
    font-size: 0.95rem;
}

.post-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.85rem;
    color: #5a6c7d;
}

.post-stats {
    display: flex;
    gap: 1rem;
}

/* 侧边栏 */
.sidebar {
    border-left: 1px solid #000;
    padding-left: 2rem;
}

.sidebar-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #000;
}

.stats-section {
    margin-bottom: 2rem;
}

.stats-section .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e1e8ed;
}

.stats-section .stat-item:last-child {
    border-bottom: none;
}

.stats-section .stat-number {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.stats-section .stat-label {
    color: #5a6c7d;
    font-size: 0.9rem;
}

.quick-actions {
    margin-bottom: 2rem;
}

.quick-actions .btn {
    margin-bottom: 0.5rem;
    display: block;
    text-align: center;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 0;
    color: #5a6c7d;
    border-bottom: 1px solid #000;
}

.empty-state p {
    margin-bottom: 1rem;
    color: #2c3e50;
}

/* 页脚 */
.footer {
    background: white;
    color: #2c3e50;
    padding: 2rem 0;
    margin-top: auto;
    border-top: 1px solid #000;
    border-bottom: 1px solid #000;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: #5a6c7d;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #2c3e50;
}



/* 版块页面头部信息 */
.category-header-info {
    background: white;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid #000;
    text-align: center;
}

.category-header-info .category-title {
    font-size: 2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.category-header-info .category-description {
    color: #5a6c7d;
    font-size: 1.1rem;
}

.posts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 0 0.5rem;
}

.posts-count {
    color: #5a6c7d;
    font-size: 0.9rem;
}

.sort-select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    background: white;
    font-family: inherit;
}

/* 帖子卡片 */
.post-card {
    background: white;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid #e1e8ed;
    display: flex;
    justify-content: space-between;
    transition: background-color 0.3s ease;
}

.post-card:hover {
    background-color: #f8f9fa;
}

.post-main {
    flex: 1;
}

.post-card .post-title {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.post-card .post-title a {
    color: #2c3e50;
    text-decoration: none;
    font-weight: 500;
}

.post-card .post-title a:hover {
    color: #3498db;
}

.post-card .post-excerpt {
    color: #5a6c7d;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.post-card .post-meta {
    display: flex;
    gap: 1.5rem;
    font-size: 0.9rem;
    color: #5a6c7d;
}

.post-card .post-meta .icon-user,
.post-card .post-meta .icon-time {
    margin-right: 0.3rem;
}

.post-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-left: 2rem;
    min-width: 80px;
}

.post-stats .stat-item {
    text-align: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border: 1px solid #e1e8ed;
}

.post-stats .stat-number {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    color: #3498db;
}

.post-stats .stat-label {
    font-size: 0.8rem;
    color: #5a6c7d;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.page-link {
    padding: 0.5rem 1rem;
    color: #3498db;
    text-decoration: none;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.page-link:hover:not(.disabled) {
    background: #3498db;
    color: white;
}

.page-link.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.page-link.disabled {
    color: #ccc;
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }

    .nav-menu {
        gap: 1rem;
    }

    .container {
        padding: 0 1rem;
    }

    .homepage-layout {
        flex-direction: column;
        gap: 2rem;
    }

    .sidebar {
        border-left: none;
        border-top: 1px solid #000;
        padding-left: 0;
        padding-top: 2rem;
        position: static;
    }

    .category-header {
        flex-direction: column;
        gap: 1rem;
    }

    .category-actions {
        margin-left: 0;
    }

    .post-card {
        flex-direction: column;
    }

    .post-stats {
        flex-direction: row;
        margin-left: 0;
        margin-top: 1rem;
        justify-content: space-around;
    }

    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .footer-links {
        justify-content: center;
    }
}

/* 帖子详情页面 */
.post-detail-page {
    max-width: 900px;
    margin: 0 auto;
}

.main-post {
    background: white;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e1e8ed;
}

.post-header {
    border-bottom: 1px solid #f1f3f4;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.main-post .post-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.8rem;
    line-height: 1.3;
}

.main-post .post-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.author-info {
    display: flex;
    align-items: center;
}

.author-details {
    display: flex;
    flex-direction: column;
}

.author-name {
    font-weight: 500;
    color: #2c3e50;
}

/* 用户名显示样式 - 昵称和识别码分开显示 */
.display-name-part {
    font-weight: 600;
    color: #1a2533; /* 昵称颜色深一点 */
}

.identifier-code-part {
    font-weight: 400;
    color: #8896a6; /* 识别码颜色浅一点 */
    font-size: 0.85em; /* 字体小一点 */
}

.post-time {
    font-size: 0.85rem;
    color: #5a6c7d;
}

.main-post .post-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.85rem;
    color: #5a6c7d;
}

.post-content {
    font-size: 1rem;
    line-height: 1.6;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.post-footer {
    border-top: 1px solid #f1f3f4;
    padding-top: 1rem;
    display: flex;
    justify-content: flex-end;
}

.post-updated {
    font-size: 0.85rem;
    color: #5a6c7d;
    font-style: italic;
}

/* 回复区域 */
.replies-section {
    background: white;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e1e8ed;
}

.replies-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.8rem;
    border-bottom: 1px solid #f1f3f4;
}

.replies-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.sort-toggle {
    color: #2c3e50;
    background-color: transparent;
    text-decoration: none;
    font-size: 0.9rem;
    cursor: pointer;
    padding: 0.4rem 0.8rem;
    border: 1px solid #2c3e50;
    transition: all 0.2s ease;
    display: inline-block;
}

.sort-toggle:hover {
    background-color: #f8f9fa;
    text-decoration: none;
}

.reply-item {
    padding: 1rem 0;
    border-bottom: 1px solid #e1e8ed;
}

.reply-item:last-child {
    border-bottom: none;
}

.reply-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.8rem;
}

.reply-author-info {
    display: flex;
    flex-direction: column;
}

.reply-author-info .author-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: #2c3e50;
}

.reply-time {
    font-size: 0.8rem;
    color: #5a6c7d;
}

.reply-content {
    font-size: 0.95rem;
    line-height: 1.5;
    color: #2c3e50;
}

.reply-number {
    font-size: 0.85rem;
    color: #5a6c7d;
    font-weight: 500;
}

/* 回复表单 */
.reply-form-section {
    background: white;
    padding: 1.5rem;
    border: 1px solid #e1e8ed;
}

.form-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.login-prompt {
    text-align: center;
    padding: 2rem;
    color: #5a6c7d;
}

.login-prompt a {
    color: #3498db;
    text-decoration: none;
}

.login-prompt a:hover {
    text-decoration: underline;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    font-size: 1rem;
    font-family: inherit;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.form-help {
    display: block;
    margin-top: 0.3rem;
    font-size: 0.9rem;
    color: #5a6c7d;
}

.form-row {
    display: flex;
    gap: 1rem;
}

.form-row .form-group {
    flex: 1;
}

.form-options {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.form-checkbox {
    width: auto;
}

.checkbox-text {
    font-size: 0.95rem;
    color: #2c3e50;
}

.form-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-top: 2rem;
}

/* 认证页面 */
.auth-page {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

.auth-container {
    background: white;
    padding: 3rem;
    border: 1px solid #e1e8ed;
    width: 100%;
    max-width: 400px;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-title {
    font-size: 2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: #5a6c7d;
    font-size: 1rem;
}

.auth-form .form-group {
    margin-bottom: 1.5rem;
}

.auth-form .form-options {
    justify-content: space-between;
    align-items: center;
}

.forgot-password {
    color: #3498db;
    text-decoration: none;
    font-size: 0.9rem;
}

.forgot-password:hover {
    text-decoration: underline;
}

.auth-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #f1f3f4;
}

.auth-link {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
}

.auth-link:hover {
    text-decoration: underline;
}

.terms-link {
    color: #3498db;
    text-decoration: none;
}

.terms-link:hover {
    text-decoration: underline;
}

/* 新增认证页面样式 */
.auth-info {
    background: #f8f9fa;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    margin-top: 1.5rem;
    border-radius: 4px;
}

.auth-info h4 {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.auth-info p {
    color: #5a6c7d;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.auth-info p:last-child {
    margin-bottom: 0;
}

.identifier-preview {
    background: #f8f9fa;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    text-align: center;
}

.identifier-preview h4 {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.preview-box {
    background: white;
    padding: 1rem;
    border: 2px solid #3498db;
    border-radius: 4px;
    margin-bottom: 0.75rem;
    font-family: 'Courier New', monospace;
    font-weight: 600;
    font-size: 1.1rem;
    color: #2c3e50;
    min-height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-container {
    max-width: 450px;
}

/* 发帖页面 */
.new-post-page {
    max-width: 900px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-size: 2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: #5a6c7d;
    font-size: 1.1rem;
}

.post-form-container {
    background: white;
    padding: 2rem;
    border: 1px solid #e1e8ed;
}

.editor-toolbar {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-bottom: none;
}

.toolbar-btn {
    padding: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.toolbar-btn:hover {
    background: #e9ecef;
}

.post-form .form-textarea {
    border-top: none;
}

.login-required {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
}

.login-prompt {
    text-align: center;
    background: white;
    padding: 3rem;
    border: 1px solid #e1e8ed;
}

.login-prompt h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.prompt-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1.5rem;
}

/* 预览区域 */
.preview-container {
    background: white;
    padding: 2rem;
    margin-top: 2rem;
    border: 1px solid #e1e8ed;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f1f3f4;
}

.preview-content {
    line-height: 1.6;
    color: #2c3e50;
}
