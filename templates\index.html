{% extends "base.html" %}

{% block title %}首页 - 简约论坛{% endblock %}

{% block content %}
<div class="homepage">
    <div class="homepage-layout">
        <!-- 左侧帖子列表 -->
        <main class="posts-main">
            {% if recent_posts %}
                {% for post in recent_posts %}
                <article class="post-item">
                    <div class="post-meta">
                        <span class="post-category">{{ post.category_name }}</span>
                        <span class="post-time">{{ post.created_at }}</span>
                    </div>
                    <h3 class="post-title">
                        <a href="{{ url_for('post_detail', post_id=post.id) }}">{{ post.title }}</a>
                    </h3>
                    <div class="post-excerpt">
                        {{ post.content[:80] }}{% if post.content|length > 80 %}...{% endif %}
                    </div>
                    <div class="post-footer">
                        <span class="post-author">{% set parts = post.username.split('#') %}{% if parts|length > 1 %}<span class="display-name-part">{{ parts[0] }}</span><span class="identifier-code-part">#{{ parts[1] }}</span>{% else %}{{ post.username }}{% endif %}</span>
                        <div class="post-stats">
                            <span class="stat-views">{{ post.views }} 浏览</span>
                        </div>
                    </div>
                </article>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <p>还没有帖子，快来发布第一个帖子吧！</p>
                    <a href="{{ url_for('new_post') }}" class="btn btn-primary">发布帖子</a>
                </div>
            {% endif %}
        </main>

        <!-- 右侧统计信息 -->
        <aside class="sidebar">
            <div class="stats-section">
                <h3 class="sidebar-title">论坛统计</h3>
                <div class="stat-item">
                    <span class="stat-number">{{ recent_posts|length }}</span>
                    <span class="stat-label">帖子总数</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">3</span>
                    <span class="stat-label">版块数量</span>
                </div>
            </div>

            <div class="quick-actions">
                <h3 class="sidebar-title">快速操作</h3>
                <a href="{{ url_for('new_post') }}" class="btn btn-outline btn-full">发布新帖</a>
            </div>
        </aside>
    </div>
</div>
{% endblock %}
